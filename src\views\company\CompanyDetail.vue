<template>
    <div class="tb-padding-12 lr-padding-16 back-color-common overflow-y-scroll">
        <div>
            <template v-if="rePortDate && riskCompleteNum === 7">
                <!-- <template v-if="rePortDate && riskCompleteNum === 7"> -->
                <div class=" t-padding-38 b-padding-16 lr-padding-12 border-radius-8 relative"
                    :style="{ background: `url(${companyBkImg})` }" style="background-size: cover;">
                    <div class="font-14">
                        {{ companyName }}
                    </div>
                    <div class="font-20 color-blue t-margin-6 font-weight-600">
                        {{ progressList[5].data?.entStatus ?  progressList[5].data?.entStatus.split('（')[0] : '-' }}
                    </div>
                    <div class="font-12 color-blue t-margin-6">
                        打败了 {{ progressList[5].data?.defeatRatio || '-' }} 的企业
                    </div>
                    <div class="t-margin-8 font-12 color-two-grey">
                        简介： {{ progressList[5].data?.operateState || '-' }}
                    </div>
                    <div id="levelLottieAnimation" class="absolute">
                        <LottieAnimation v-if="animationLevel.level === 1" :animationData="levelAnimation1"
                            :loop="false" :autoplay="true" :width="'100%'" :height="'100%'" />
                        <LottieAnimation v-else-if="animationLevel.level === 2" :animationData="levelAnimation2"
                            :loop="false" :autoplay="true" :width="'100%'" :height="'100%'" />
                        <LottieAnimation v-else-if="animationLevel.level === 3" :animationData="levelAnimation3"
                            :loop="false" :autoplay="true" :width="'100%'" :height="'100%'" />
                        <LottieAnimation v-else-if="animationLevel.level === 4" :animationData="levelAnimation4"
                            :loop="false" :autoplay="true" :width="'100%'" :height="'100%'" />
                        <LottieAnimation v-else-if="animationLevel.level === 5" :animationData="levelAnimation5"
                            :loop="false" :autoplay="true" :width="'100%'" :height="'100%'" />

                    </div>
                    <span class="absolute  animationLevelVal" v-if="animationLevel.val"><span
                            class="color-blue font-20 font-bold">{{
                                animationLevel.val
                            }}</span>
                        <span class="color-blue font-12">{{ animationLevel.type === 'rate' ? '级' : '分'
                            }}</span>

                    </span>
                </div>
                <div class="display-flex top-bottom-center t-margin-15 space-between">
                    <div class="font-14">共检测 {{ riskCountNum }} 项指标，发现高风险 <span class="color-red">{{ riskCountHighNum
                            }}</span> 项</div>
                    <div @click="seeContact">
                        <div class="btn" style="width: 3rem;">联系方式
                            <span v-if="channelType === 1">({{ contactTotal }})条</span>
                        </div>
                    </div>
                </div>
                <div class="t-margin-8 display-flex top-bottom-center all-padding-12 gap-12 see-report-box">
                    <div class="report-logo">
                        <img src="@/assets/images/see-report-icon.png" mode="scaleToFill" style="width: 1.5rem;height: 1.5rem;"/>
                    </div>
                    <div class="flex-1">
                        <div class="font-16">企业完整报告</div>
                        <div class="font-12 color-two-grey t-margin-4">工商司法更新时间:{{
                            $moment(rePortDate).format('YYYY-MM-DD') }}</div>
                        <div class="font-12 color-two-grey t-margin-2">票税数据更新时间:{{ collectDate ?
                            $moment(collectDate).format('YYYY-MM-DD') : '-' }}</div>
                    </div>
                    <div class="report-btn">
                        <div class="btn" @click="seeReport">去查看</div>
                    </div>
                </div>
                <div class="tabs-box">
                    <van-tabs v-model="progressActive">
                        <template v-for="item in progressList">
                            <van-tab v-if="!item.hide" :key="item.type" :name="item.type" :title="item.name">
                                <SpecialInspection v-if="item.data" :progressData="item.data" :progressName="item.name"
                                    :progressTxt="item.txt" :progressType="item.type" :from="'normal'">
                                </SpecialInspection>
                            </van-tab>
                        </template>

                    </van-tabs>
                </div>
                <div class="t-margin-20">
                    <PolicyMatch />
                </div>
                <div class="t-margin-20">
                    <ProductMatch />
                </div>
                <div class="t-margin-20">
                    <SeekHelp />
                </div>

            </template>
            <template v-else>

                <div class="display-flex gap-20 ">
                    <div class="font-20 color-blue"> {{ companyName }}</div>
                </div>

                <div class="display-flex t-margin-20" style="align-items: start;">
                    <div class="display-flex flex-1" style="align-items: start;">
                        <div style="width:20px;margin-top:3px">
                            <LottieAnimation :animationData="loadingIcon" :loop="true" :autoplay="true" />
                        </div>
                        <div>
                            <LottieAnimation :animationData="loadingText" :loop="false" :autoplay="true" />
                        </div>
                    </div>

                    <div class="loading-box">
                        <LottieAnimation :animationData="companyLoading" :loop="true" :autoplay="true" />
                    </div>

                </div>


            </template>

        </div>
    </div>
</template>

<script setup lang="ts">
import { showDialog, showConfirmDialog } from 'vant'
import { ref, onMounted, provide, computed } from 'vue'
import type { Ref } from 'vue'

import type {
    companyBasicData,
    companyFinanceData,
    companyInvoiceData,
    companyTaxData,
    companyRelatedData
} from '@/types/indicator'

import { useRouter, useRoute } from 'vue-router'

import reportService from '@/service/reportService'
import aicService from '@/service/aicService'
import companyService from '@/service/companyService'
import orderService from '@/service/orderService'

import SpecialInspection from '@/components/special-inspection/SpecialInspection.vue'
import PolicyMatch from '@/components/policy-match/PolicyMatch.vue'
import ProductMatch from '@/components/product-match/ProductMatch.vue'

import levelAnimation1 from '@/assets/lottiefiles/level-1.json'
import levelAnimation2 from '@/assets/lottiefiles/level-2.json'
import levelAnimation3 from '@/assets/lottiefiles/level-3.json'
import levelAnimation4 from '@/assets/lottiefiles/level-4.json'
import levelAnimation5 from '@/assets/lottiefiles/level-5.json'

import companyLoading from '@/assets/lottiefiles/company-loading.json'
import loadingText from '@/assets/lottiefiles/loading-text.json'
import loadingIcon from '@/assets/lottiefiles/loading-icon.json'

import detailInfoBlueBk from '@/assets/images/detail-info-blue-bk.png'
import detailInfoOrangeBk from '@/assets/images/detail-info-orange-bk.png'
import { split } from 'lodash'

const socialCreditCode: Ref<string> = ref('')
provide('socialCreditCode', socialCreditCode)

const router = useRouter()

const route = useRoute()

interface progressType {
    name: string,
    type: string,
    txt?: string,
    method: keyof typeof reportService,
    hide: boolean,
    data: companyBasicData | companyFinanceData | companyInvoiceData | companyTaxData | companyRelatedData | null
}

const progressList: Ref<progressType[]> = ref([
    {
        name: '基础项目',
        type: 'basic',
        method: 'getBasicResult',
        hide: false,
        data: null
    },
    {
        name: '财务项目',
        type: 'finance',
        txt: '纳税',
        method: 'getFinanceResult',
        hide: false,
        data: null
    },
    {
        name: '税务项目',
        type: 'tax',
        txt: '纳税',
        method: 'getTaxResult',
        hide: false,
        data: null
    },
    {
        name: '发票项目',
        type: 'invoice',
        txt: '发票',
        method: 'getInvoiceResult',
        hide: false,
        data: null
    },
    {
        name: '关联项目',
        type: 'related',
        method: 'getRelatedResult',
        hide: false,
        data: null
    },
    {
        name: '经营状态',
        type: 'operate',
        method: 'getOperateStateResult',
        hide: true,
        data: null
    },
    {
        name: '生命周期',
        type: 'lifeCycle',
        method: 'getLifeCycleResult',
        hide: true,
        data: null
    }
])

const progressActive: Ref<string> = ref('basic')

const collectDate = ref<number>(0)
const getCollectInfo = () => {
    reportService.collectPage({ page: 1, pageSize: 1, status: 'SUCCESS', socialCreditCode: socialCreditCode.value, collectType: 'INVOICE' }).then(res => {
        console.log(res)
        if (res.data.length) {
            collectDate.value = res.data[0].executionTime
        }
    })
}

const riskCompleteNum = ref(0)

const getRiskData = () => {
    progressList.value
        .filter(item => item.method in reportService)
        .forEach(item => reportService[item.method]({ socialCreditCode: socialCreditCode.value }).then(res => {
            riskCompleteNum.value += 1

            if (res.data) {
                console.log(item)
                item['data'] = res.data
            }
        }))

}

const riskCountNum = computed(() => {

    let total = 0
    for (let item of progressList.value) {
        if (item.data?.dataList && !item.hide) {
            total += item.data.dataList.filter((i) => {
                return i.level !== null || i.list
            }).length
        }
    }
    // return this.riskData.totalNum - total;
    return total

})

const riskCountHighNum = computed(() => {
    let total = 0
    for (let item of progressList.value) {
        if (item.data && !item.hide) {
            total += item.highRisk || 0
        }
    }
    return total
})

const companyName = ref('')

const contactTotal = ref<number>(0)

const isLock = ref<boolean>(false)

const channelType = ref<number>(0)

const getContactNum = () => {
    aicService.gsGetContacts({ socialCreditCode: socialCreditCode.value }).then((res) => {
        contactTotal.value = res.contactNum || 0

        isLock.value = res.isLock === 1 ? true : false

        channelType.value = res.channelType

    })
}


const companyBkImg = computed(() => {
    let operateData = progressList.value[5].data

    if (!progressList.value[2].noCollect) {
        //如果还没采集取评级


        if (!operateData || !operateData.rate) {
            return ''
        }

        console.log('operateData')
        if (['S', 'A', 'B'].find((item) => { return item === operateData.rate })) {
            return detailInfoBlueBk
        } else {
            return detailInfoOrangeBk
        }

    } else {
        //采集了取得分
        if (!operateData || !operateData.score) {
            return ''
        }
        if (operateData.score > 60) {
            return detailInfoBlueBk
        } else {
            return detailInfoOrangeBk
        }

    }
})


const animationLevel = computed(() => {
    let operateData = progressList.value[5].data

    if (!progressList.value[2].noCollect) {
        //如果还没采集取评级

        let arr = ['D', 'C', 'B', 'A', 'S']

        if (!operateData || !operateData.rate) {
            return {
                level: 0
            }
        }


        let idx = arr.findIndex(item =>


            item === operateData.rate
        )

        console.log(idx)

        return {
            level: idx + 1,
            type: 'rate',
            val: operateData?.rate || '-'
        }

    } else {
        //采集了取得分
        let score = operateData.score
        console.log(score)
        let level = 0
        if (score < 20) {
            level = 1
        } else if (score < 40) {
            level = 2
        } else if (score < 60) {
            level = 3
        } else if (score < 80) {
            level = 4
        } else {
            level = 5
        }
        return {
            level,
            type: 'score',
            val: operateData?.score || '-'
        }

    }

})

const init = () => {
    //获取最近的发票采集时间
    getCollectInfo()
    getRiskData()
    getContactNum()
    //获取基础信息是否已经采集完毕

}

const rePortDate = ref<string>('')

let tm = null

onMounted(() => {

    if (route.query.socialCreditCode) {
        socialCreditCode.value = route.query.socialCreditCode as string
        companyName.value = route.query.companyName as string


        //看报告是否生成，生成了，不显示动画，直接初始化数据
        companyService.getBasicInfo({
            socialCreditCode: socialCreditCode.value,
        }).then(res => {
            rePortDate.value = res.reportDate || ''

            if (rePortDate.value) {
                //已经生成了
                init()
            } else {
                //还未生成
                tm = setInterval(() => {
                    companyService.getBasicInfo({
                        socialCreditCode: socialCreditCode.value,
                    }).then(res => {
                        if (res.reportDate) {
                            rePortDate.value = res.reportDate
                            clearInterval(tm)
                            init()
                        }
                    })
                }, 1000)
            }
        })

        //未生成，则显示动画


    } else {
        showDialog({
            title: '提示',
            message: '缺少税号，无法体检',
        }).then(() => {
            router.back()
        })

    }

})


const seeReport = () => {
    router.push({
        name: 'companyReport',
        query: {
            socialCreditCode: socialCreditCode.value,
            companyName: companyName.value,
        }
    })
}

const seeContact = async () => {
    let data = {
        socialCreditCode: socialCreditCode.value,
        companyName: companyName.value,
    }
    if (isLock.value) {
        let lessRes = await orderService.orderServiceStatistics({ serviceKeys: 'xs' })

        if (lessRes[0] && Number(lessRes[0]) <= 0) {
            router.push({
                name: 'goodsList',
                query: {
                    goods: 'leads-group',
                },
            })
            return
        }


        showConfirmDialog({
            title: '联系方式查看',
            message:
                '本次将会扣除一次权益，是否确认查看联系方式？',
        })
            .then(() => {
                orderService.orderBuyLegal({
                    ...data,
                    serviceKey: 'xs'
                }).then(() => {
                    router.push({
                        name: 'contactList',
                        query: data
                    })
                })

            })
            .catch(() => {

            })

    } else {
        router.push({
            name: 'contactList',
            query: data
        })
    }
}

</script>

<style scoped>
.loading-box {
    width: 100px;
}

.report-logo {
    width: 1.5rem;
    height: 1.5rem
}

.report-btn {
    width: 70px;
}

.see-report-box {
    background-image: url('@/assets/images/see-report-bg.png');

}

.tabs-box {
    --van-tabs-nav-background: transparent
}

#levelLottieAnimation {
    top: 0;
    right: 28px;
    z-index: 2;
    width: 25vw
}

.animationLevelVal {
    top: 0.38rem;
    right: 1.6rem;
    z-index: 3;
}
</style>
