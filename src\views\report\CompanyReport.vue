<template>
    <div class="report-container">
        <van-tabs v-model:active="activeType" @change="tabChange" :sticky="true" :line-width="'50%'">
            <van-tab :title="report.name" :name="report.key" v-for="report in reportList" :key="report.key">
                <div class="back-color-common report-list-box lr-padding-16 tb-padding-16 ">
                    <div class="font-14 color-two-grey">
                        剩余额度：{{ report.lessEdu }} 份
                    </div>
                    <ReportListItem class="b-padding-40" :list="collectList" :type="report.key"
                        :socialCreditCode="socialCreditCode" />

                    <div style="height: 100px"></div>
                    <div class="fixed collect-btn">
                        <div class="btn" style="width: 100%;height: 1rem;" @click="getCollectUrl(report.key)">{{ !collectList.length ? '授权' : '更新' }}报告
                        </div>
                    </div>
                </div>
            </van-tab>
        </van-tabs>
    </div>
    <van-popup v-model:show="showCollect" :style="{ padding: '16px', borderRadius: '8px 8px 0 0' }" position='bottom'>
        <div class="backcolor-white text-center border-radius-12">
            <div class="display-flex relative left-right-center b-padding-15 top-bottom-center">
                <div class="absolute" style="right: 0;top:-10px" @click="showCollect = false">

                    <icon icon="icon-x-02" :size="12" color="" />
                </div>
                <div class="font-16">认证服务</div>
            </div>
            <div class="display-flex tb-padding-15 gap-6">
                <div class="lr-padding-10 display-flex top-bottom-center  btn border-radius-8 flex-1"
                    @click="goUrlCollect()" style="height: 1.5rem;">

                    <icon icon="icon-renzheng" :size="23" color="#ffffff" />

                    <div class="border-radius-8 flex-1 display-flex flex-column">
                        <div class="font-16">直接认证</div>
                        <div class="font-12 t-margin-5">账密/税局APP扫码</div>
                    </div>
                </div>
                <div class="lr-padding-10 display-flex top-bottom-center scan-box border-radius-8 flex-1"
                    @click="goQrcodeCollect()">

                    <icon icon="icon-scan1" :size="23" color="#ffffff" />

                    <div class="border-radius-8 flex-1">
                        <div class="font-16">面对面扫码</div>
                        <div class="font-12 t-margin-5">微信/支付宝/浏览器</div>
                    </div>
                </div>
            </div>
            <div class="b-padding-30">
                <div class="border text-center tb-padding-5 flex-center">
                    <icon icon="icon-Frame2" :size="16" color="#3C74EB" /> <span class="font-16"
                        @click="copyUrl">复制链接给好友认证</span>
                </div>

            </div>
        </div>
    </van-popup>

    <van-popup v-model:show="showQrcodeCollect" :style="{ padding: '16px', borderRadius: '8px', width: '80% ' }">
        <div class="display-flex relative left-right-center b-padding-15 top-bottom-center">
            <div class="absolute" style="right: 0;top:-10px" @click="showQrcodeCollect = false">

                <icon icon="icon-a-Frame1171276285" :size="12" color="" />
            </div>
            <div class="font-16">认证服务</div>
        </div>

        <div class="back-color-blue color-white text-center border-radius-8 font-16 tb-padding-10">
            <div>{{ companyName }}</div>
            <div class="t-margin-4">{{ socialCreditCode }}</div>

        </div>

        <div id="collectQrcode" v-if="collectUrl" class="t-margin-10 lr-paddding-10 display-flex left-right-center">
            <vue-qr :text="collectUrl" :size="200"></vue-qr>
        </div>

        <div class="font-14 text-center t-margin-10">
            <div>请使用 <span class="color-blue">微信</span>/<span class="color-blue">支付宝</span>进行 </div>
            <div>扫码登录授权页面 </div>
        </div>
        <div class="font-14 t-margin-10">
            <div>温馨提示：</div>
            <div>1：此二维码24小时内有效请尽快在有效期内完成扫码</div>
            <div>2：扫码前，请确保您的手机网络连接稳定且信号良好</div>
            <div>3：操作过程中，如遇到任何问题或困难，请及时联系客服人员</div>
        </div>
    </van-popup>

</template>

<script lang='ts' setup>
import { ref, onMounted, provide } from 'vue'
import { showDialog, showConfirmDialog, showToast } from 'vant'
import vueQr from 'vue-qr/src/packages/vue-qr.vue'
import reportService from '@/service/reportService'
import orderService from '@/service/orderService'
import collectService from '@/service/collectService'
import type { Ref } from 'vue'
import type { ReportItem } from '@/types/report'
import type { SearchCollectLogItem } from '@/types/report'
import { useRoute, useRouter } from 'vue-router'
import ReportListItem from './components/ReportListItem.vue'

const router = useRouter()
const route = useRoute()

const socialCreditCode: Ref<string> = ref('')
const companyName: Ref<string> = ref('')

provide('socialCreditCode', socialCreditCode)

const collectList: Ref<SearchCollectLogItem[]> = ref([])

const tabChange = (type: string) => {
    if (type === 'fpbg') {
        getCollectInfo('INVOICE')
    } else if (type === 'swbg') {
        getCollectInfo('TAX')
    } else {
        collectList.value = []
    }

}

const getCollectInfo = (type: string) => {
    reportService.collectPage({ page: 1, pageSize: 9999, status: 'SUCCESS', socialCreditCode: socialCreditCode.value, collectType: type }).then(res => {
        console.log(res)
        collectList.value = res.data
    })
}


const loading: Ref<boolean> = ref(false)

const reportList: Ref<ReportItem[]> = ref([])

const getReportList = () => {
    loading.value = true
    reportService
        .getReportList({ socialCreditCode: socialCreditCode.value })
        .then((res) => {
            console.log(res)
            reportList.value = res.filter(item => item.key !== 'gqbg')

            for (let report of reportList.value) {
                orderService.orderServiceStatistics({ serviceKeys: report.key }).then(res => {
                    report.lessEdu = res[0]
                })
            }
        })
        .finally(() => {
            loading.value = false
        })
}

const activeType = ref('fpbg')

onMounted(() => {
    if (route.query.socialCreditCode) {
        socialCreditCode.value = route.query.socialCreditCode as string
        companyName.value = route.query.companyName as string
        getCollectInfo('INVOICE')
        getReportList()
        // getLessEdu()
    } else {
        showDialog({
            title: '提示',
            message: '缺少税号',
        }).then(() => {
            router.back()
        })
    }
})

const showCollect = ref<boolean>(false)

const collectUrl = ref<string>('')

const getCollectUrl = async (key: string) => {
    console.log('点击了授权链接', activeType.value)

    let lessRes = await orderService.orderServiceStatistics({ serviceKeys: key })

    if (lessRes[0] && Number(lessRes[0]) <= 0) {
        router.push({
            name: 'goodsList',
            query: {
                goods: 'combo-group',
            },
        })
        return
    }

    collectService.getAuthUrl({
        socialCreditCode: socialCreditCode.value,
        deductType: key
    }).then(res => {
        console.log(res)
        let isBuy = reportList.value.find(item => item.key === activeType.value)?.isBuy
        collectUrl.value = res.url
        if (isBuy) {
            showCollect.value = true
        } else {
            showConfirmDialog({
                title: '提示',
                message:
                    '本次授权成功后,会扣除一份报告权益,是否确认生成报告链接?',
            })
                .then(() => {
                    showCollect.value = true
                })
                .catch(() => {
                })
        }
    }).finally(() => {
    })
}

const copyUrl = () => {
    navigator.clipboard.writeText(collectUrl.value).then(function () {
        showToast('复制成功')
        showCollect.value = false
    }).catch(function (err) {
        showToast(`无法复制文本:${err}`)
        console.error()
    })
}

const goUrlCollect = () => {
    window.open(collectUrl.value)
}

const showQrcodeCollect = ref<boolean>(false)
const goQrcodeCollect = () => {
    showCollect.value = false
    showQrcodeCollect.value = true

}

</script>

<style lang='scss' scoped>
.report-list-box {
    height: calc(100vh - 50px);
}

.collect-btn {
    bottom: 10px;
    width: calc(100% - 32px);
}

.icon {
    width: 25px;
    height: 25px
}

.scan-box {
    background: linear-gradient(98.52deg, #ffcd97 2.65%, #ff8a3c 93.37%);
    color: #fff;
}

.close {
    width: 20px;
    height: 20px;
}

.collectQrcode {

    width: 200px;
    height: 200px;

}
</style>
