<template>
    <div class="t-margin-10">
        <div class="border-radius-16 border box oh" :style="{ 'border-color': mainColor.color }">
            <div class="tb-padding-8 lr-padding-10 display-flex top-bottom-center space-between special-inspection-title "
                 @click="expandMore" :class='backClass'>
                <div class="display-flex top-bottom-center">
                    <icon :icon="mainColor.icon" :size="31" />
                    <div class="l-padding-10 display-flex flex-column">
                        <div class="font-16 ">
                            <span class="font-bold">{{ progressName }}</span>
                            <div v-if='(!oemInfo || !oemInfo.hideMatching)' style="height: 0.5rem;">
                                <text class="font-14 color-two-grey" style="color: #666666;"
                                      v-if="!progressData.noCollect && progressData.riskNum == 0">
                                    暂无风险项，非常健康，继续保持
                                </text>
                                <text class="font-14 color-two-grey" v-else-if="!progressData.noCollect">共<text
                                    class="color-red">{{ progressData.riskNum }}</text>项风险，其中高风险<text
                                    class="color-red">{{ progressData.highRisk }}</text>项</text>
                                <text v-else class="font-14 color-two-grey">{{ progressType == 'basic' || progressType ==
                                    'related' ? '正在检测中' : `${progressTxt}数据缺失，暂无检测结果` }}</text>
                            </div>

                        </div>
                        
                    </div>
                </div>

            </div>
            <!-- 展开收起 -->
            <div v-if="expand">
                <!-- 体检报告 -->
                <div style="border-bottom: 1rpx solid rgba(150, 180, 255, 0.2);" class="lr-padding-16  tb-padding-8"
                     v-if='!progressData.noCollect'>
                    <physical-examination-summary :desc="progressData.desc || progressData.description"
                                                  :backClass='backClass'></physical-examination-summary>
                </div>
                <!-- 检测列表 -->
                <div class=" lr-padding-16  tb-padding-8" v-if="progressData.dataList">
                    <SpecialInspectionDetail :dataList="progressData.dataList" :progressType="progressType"
                                             :noCollect='progressData.noCollect || false'></SpecialInspectionDetail>
                </div>
            </div>
        </div>

        <!-- <div v-else-if="!progressData && progressType == 'related'" class="border-radius-8 border-blue">
			<div
				class="all-padding-24 display-flex top-bottom-center space-between border-radius-8 special-inspection-blue">
				<div class="display-flex top-bottom-center">
					<image src="../../static/common/icon/success-icon.png" class="danger-icon r-margin-16"></image>
					<div>
						<div class="font-16font-bold color-white">{{progressName}}</div>
						<div class="font-14 color-white">暂无风险项</div>
					</div>
				</div>
			</div>
		</div> -->
    </div>
</template>

<script setup>
import {
    ref,
    defineProps,
    onMounted,
    computed,
    provide
} from 'vue'
import { useStore } from 'vuex' //引入mapState

import SpecialInspectionDetail from '../special-inspection-detail/SpecialInspectionDetail.vue'
import PhysicalExaminationSummary from '../physical-examination-summary/PhysicalExaminationSummary.vue'
// 展开收起
const expand = ref(true)
const expandMore = () => {
    expand.value = !expand.value
}
const props = defineProps({
    progressData: Object,
    progressType: String,
    progressName: String,
    isCollectRisk: Boolean,
    collectType: String,
    progressTxt: String,
    from: String,
})


const mainColor = computed(() => {
    let d = props.progressData

    if (d.riskNum > 0) {
        return {
            color: '#E95133',
            icon: 'icon-red',
            cla: 'gradientRed'
        }
    } else if (!d.noCollect && d.riskNum === 0) {
        return {
            color: '#00B273',
            icon: 'icon-green',
            cla: 'gradientGreen'
        }
    } else {
        return {
            color: '#3C74EB',
            icon: 'icon-blue',
            cla: 'back-color'
        }
    }
})

provide('mainColor', mainColor)

const backClass = computed(() => {
    let d = props.progressData

    if (d.riskNum > 0) {
        return 'gradientRed'
    } else if (!d.noCollect && d.riskNum === 0) {
        return 'gradientGreen'
    } else {
        return 'back-color-2b-gradient-blue-30'
    }
})
const store = useStore()
const oemInfo = computed(() => store.state.oemInfo)

onMounted(() => {
    if (!props.progressData?.dataList?.length) {
        expand.value = false
    }
})


</script>

<style lang="scss" scoped>
.border {
    border: 0.5rpx solid;
}



.special-inspection-red {
    // background: linear-gradient(184.59deg, rgba(13, 35, 76, 1) 0%, rgba(50, 46, 84, 1) 100%);
}

.special-inspection-blue {
    // background: linear-gradient(187.27deg, rgba(14, 49, 115, 1) 0%, rgba(14, 44, 102, 1) 100%);
}

.special-inspection-title {
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
}

.danger-icon {
    height: 64rpx;
    width: 64rpx;
}

.gradientRed {
    background: linear-gradient(to bottom, rgba(233, 81, 51, 0.31), rgba(233, 81, 51, 0.05));
}

.gradientGreen {
    background: linear-gradient(to right, rgba(0, 178, 115, 0.3), rgba(0, 178, 115, 0.1));
}

.gradientBlue {
    background: linear-gradient(to right, rgba(13, 50, 123, 0.3), rgba(13, 35, 76, 0.1));
}

.collectButton {
    border-radius: 8rpx;
    padding: 12rpx 36rpx;
    color: #fff;
    font-size: 28rpx;
    font-weight: 700;
    background: linear-gradient(145.17deg, rgba(13, 138, 255, 0.74) 0%, rgba(15, 54, 133, 1) 100%);
}

.box {}
</style>